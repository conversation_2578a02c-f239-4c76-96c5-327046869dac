import {LOAD_DOCS_RENEW,LOAD_DOCS_RENEW_SUCCESS,LOAD_DOCS_RENEW_ERROR, VIEW_DOCUMENT} from './constants';

export function loadDocumentsRenew(page,zoneId,filters) {
    return {
        type: LOAD_DOCS_RENEW,
        page,
        zoneId,
        filters,
    };
};

export function documentsRenewLoaded(documentsRenewList) {
    return {
        type: LOAD_DOCS_RENEW_SUCCESS,
        documentsRenewList,
    };
};

export function documentsRenewLoadingError(error) {
    return {
        type: LOAD_DOCS_RENEW_ERROR,
        error,
    };
};

export function viewDocument(documentId) {
    return {
        type: VIEW_DOCUMENT,
        documentId,
    };
};