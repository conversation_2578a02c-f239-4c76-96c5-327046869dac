import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Field, Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import { <PERSON><PERSON>, But<PERSON>, Modal } from 'react-bootstrap';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import SchoolLevels from 'containers/Common/SubComponents/SchoolLevels';
import Select from 'react-select';
import { EDIT_ICON, USER_PICTURE } from 'components/Common/ListIcons/ListIcons';
import convertBase64 from 'containers/Common/Scripts/ConvertFileToBase64';
import <PERSON>ropper from 'react-cropper';
import {
  makeSelectAssistant,
  makeSelectDisponibleZones,
  makeSelectError,
  makeSelectLanguageCommunications,
  makeSelectSuccessAddOrUpdateAssistant,
  makeSelectZonesList,
} from './selectors';
import assistantReducer from './reducer';
import assistantSaga from './saga';
import {
  addAssistantRequest,
  loadAssistant,
  loadDisposableZones,
  loadLanguageCommunications,
  loadZonesList,
  resetAddAssistant,
  resetError,
  resetUpdateAssistant,
  updateAssistantRequest,
} from './actions';
import ZoneChangeModal from '../Assistants/ZoneChangeModal';
import {
  changeZoneRequest,
  changeZoneReset,
  fetchAssistantsRequest,
} from '../Assistants/actions';
import { makeSelectChangeZoneSuccess } from '../Assistants/selectors';
import 'cropperjs/dist/cropper.css';
const keyAssistant = 'addAssistant';
const initialValues = {
  picture: [],
  email: '',
  address: '',
  languageCommunicationIds: [],
  firstName: '',
  lastName: '',
  birthDate: '',
  phone: '',
  cinNumber: '',
  schoolLevel: {
    id: '',
  },
};

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Adresse email invalide')
    .required("L'email est requis"),
  // address: Yup.string().required("L'adresse est requise"),
  // No zone validation
  // birthDate: Yup.string()
  //   .required('La date de naissance est requise')
  //   .test(
  //     'birthDate-in-past',
  //     'La date de naissance doit être dans le passé',
  //     (value) => value && new Date(value) < new Date()
  //   ),
});

const assistantSelector = createStructuredSelector({
  assistant: makeSelectAssistant,
  error: makeSelectError,
  success: makeSelectSuccessAddOrUpdateAssistant,
  disposableZones: makeSelectDisponibleZones,
  changeZoneSuccess: makeSelectChangeZoneSuccess,
  languageCommunications: makeSelectLanguageCommunications,
  zones: makeSelectZonesList,
});

const inputStyle = {
  borderRadius: '35px',
  border: '1px solid #4F89D780',
  width: '100%',
  padding: '15px',
  margin: '5px 0',
  paddingBottom: '15px',
};

const labelStyle = {
  fontWeight: '600',
  marginBottom: '5px',
};

const dangerStyle = {
  fontSize: '0.9rem',
  marginLeft: '5px',
};
const disabledInputStyle = {
  width: '100%',
  padding: '10px',
  margin: '5px 0',
  borderRadius: '35px',
  border: '1px solid #ced4da',
  backgroundColor: '#e9ecef',
  cursor: 'not-allowed',
};

const translateError = (errorDetail, isUpdateMode) => {
  if (isUpdateMode) {
    if (errorDetail.includes('Assistant with email')) {
      return 'Erreur lors de la modification: un assistant ou un utilisateur avec le même email existe déjà';
    }
    return 'Erreur lors de la modification: la zone est déjà attribuée à un autre assistant';
  }
  if (errorDetail.includes('Assistant with email')) {
    return "Erreur lors de l'ajout: un assistant ou un utilisateur avec le même email existe déjà";
  }
  if (
    errorDetail.includes('The zone is already assigned to another assistant')
  ) {
    return "Erreur lors de l'ajout: la zone est déjà attribuée à un autre assistant";
  }
  if (errorDetail.includes('Error loading user with email')) {
    return "Erreur lors de l'ajout: l'email de l'utilisateur n'existe pas";
  }

  return 'Une erreur est survenue';
  // also scroll the window to the top with smooth behavior
};

export default function AssistantForm() {
  useInjectReducer({ key: keyAssistant, reducer: assistantReducer });
  useInjectSaga({ key: keyAssistant, saga: assistantSaga });
  const {
    assistant,
    error,
    success,
    disposableZones,
    changeZoneSuccess,
    languageCommunications,
    zones,
  } = useSelector(assistantSelector);
  const dispatch = useDispatch();
  const history = useHistory();
  const params = useParams();
  const formikRef = useRef();
  const cropperRef = useRef(null);
  const location = useLocation();

  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [showCropper, setShowCropper] = useState(false);

  const isUpdateMode = Boolean(params.idAssistant);
  const isReadMode = Boolean(params.isRead);
  const [showDateFields, setShowDateFields] = useState(false);

  const initialCall = useCallback(() => {
    dispatch(resetAddAssistant());
    dispatch(resetError());
    dispatch(loadDisposableZones());
    setShowAlert(false);
    dispatch(loadLanguageCommunications());
    dispatch(loadZonesList());
  }, []);

  useEffect(() => {
    initialCall();
  }, []);

  useEffect(() => {
    if (isUpdateMode) {
      dispatch(loadAssistant(params.idAssistant));
    }
  }, [dispatch, isUpdateMode, params.idAssistant, disposableZones]);

  useEffect(() => {
    if (assistant && isUpdateMode) {
      if (!assistant.picture64) {
        setUploadedPicture(USER_PICTURE);
      } else {
        setUploadedPicture(
          `data:image/jpeg;base64,${atob(assistant.picture64)}`,
        );
      }
      formikRef.current.setValues({
        id: assistant.id,
        email: assistant.email,
        address: assistant.address,
        firstName: assistant.firstName,
        lastName: assistant.lastName,
        phone: assistant.phone,
        cinNumber: assistant.cinNumber,
        birthDate: assistant.birthDate ? assistant.birthDate.split('T')[0] : '',
        languageCommunicationIds: assistant.languageCommunicationIds || [],
        schoolLevel: {
          id: assistant.schoolLevel ? assistant.schoolLevel.id : '',
        },
      });
    }
  }, [assistant, isUpdateMode]);

  useEffect(() => {
    if (error) {
      setFormSubmitted(false);
      const translatedMessage = translateError(error, isUpdateMode);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setMessage(
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{translatedMessage}</p>
        </Alert>,
      );
      setShowAlert(true);
      dispatch(resetError());
    }
  }, [error, isUpdateMode]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  const handleSubmit = (values, { setSubmitting }) => {
    const payload = {
      ...values,
    };
    if (isUpdateMode) {
      dispatch(
        updateAssistantRequest({ id: params.idAssistant, data: payload }),
      );
    } else {
      dispatch(addAssistantRequest(payload));
    }
    setFormSubmitted(true);
  };

  const handleCancel = () => {
    history.push('/assistants');
  };

  const [showChangeZoneModal, setShowChangeZoneModal] = useState(false);
  const [assistantIdForChangeZone, setAssistantIdForChangeZone] = useState(
    null,
  );
  const [zoneIdForChangeZone, setZoneIdForChangeZone] = useState(null);
  const [
    dateAffectationForChangeZone,
    setDateAffectationForChangeZone,
  ] = useState(null);
  const [endDateForChangeZone, setEndDateForChangeZone] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessages, setErrorMessages] = useState([]);

  const handelChangerZone = (assistantId, zoneId, dateAffectation, endDate) => {
    setAssistantIdForChangeZone(assistantId);
    setZoneIdForChangeZone(zoneId);
    setDateAffectationForChangeZone(dateAffectation);
    setEndDateForChangeZone(endDate);
    setShowChangeZoneModal(true);
  };

  useEffect(() => {
    if (changeZoneSuccess) {
      setSuccessMessage('Zone modifiée avec succès');
      dispatch(changeZoneReset());
      setShowAlert(true);
      dispatch(fetchAssistantsRequest(0));
      handleCloseChangeZoneModal();
      dispatch(loadAssistant(assistantIdForChangeZone));
    }
  }, [changeZoneSuccess, dispatch]);

  const handleCloseChangeZoneModal = () => {
    setShowChangeZoneModal(false);
    setAssistantIdForChangeZone(null);
    setZoneIdForChangeZone(null);
    setDateAffectationForChangeZone(null);
    setEndDateForChangeZone(null);
    dispatch(changeZoneReset());
    setErrorMessages([]);
  };
  const languageOptions =
    languageCommunications &&
    languageCommunications.map(language => ({
      value: language.id,
      label: language.name,
    }));
  const [uploadedPicture, setUploadedPicture] = useState(USER_PICTURE);

  const handleSaveCroppedImage = () => {
    if (cropperRef.current) {
      const croppedCanvas = cropperRef.current.cropper.getCroppedCanvas();
      if (croppedCanvas) {
        const croppedImage = croppedCanvas.toDataURL();
        setShowCropper(false);

        const croppedFile = base64ToFile(croppedImage, 'cropped_image.png');
        const base64 = convertBase64(croppedFile);
        base64
          .then(data => {
            setUploadedPicture(data);
            formikRef.current.setFieldValue('picture', croppedFile);
          })
          .catch(error => console.error(error));
      } else {
        console.error("Le cropper n'a pas généré de canevas.");
      }
    } else {
      console.error("Le cropper n'est pas correctement initialisé.");
    }
  };

  const base64ToFile = (base64Data, filename = 'cropped_image.png') => {
    const arr = base64Data.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const byteString = atob(arr[1]);

    const byteArray = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      byteArray[i] = byteString.charCodeAt(i);
    }

    return new File([byteArray], filename, { type: mime });
  };

  return (
    <div className="sub-container mx-auto my-4" style={{maxWidth: '700px'}}>
      {showAlert && message}
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        validateOnBlur={false}
        onSubmit={handleSubmit}
        innerRef={formikRef}
        enableReinitialize={true}
      >
        {({ isSubmitting, errors, touched, setFieldValue, values }) => (
          <Form>
            <h3 style={{ textAlign: 'center', marginBottom: '20px' }}>
              {isReadMode
                ? "Consulter l'assistant"
                : isUpdateMode
                ? "Modifier l'assistant"
                : 'Ajouter un assistant'}
            </h3>
            <div className="d-flex justify-content-center">
              <img
                src={uploadedPicture}
                style={{ textAlign: 'center' }}
                className="rounded-circle row"
                width="100px"
                height="100px"
                alt="Profile"
                ref={cropperRef}
              />
              {!isReadMode && (
                <label
                  style={{ cursor: 'pointer', marginLeft: '10px' }}
                  htmlFor="upload"
                >
                  <img
                    src={EDIT_ICON}
                    width="20px"
                    height="20px"
                    title="Modifier la photo"
                  />
                </label>
              )}

              <input
                type="file"
                id="upload"
                hidden
                className="row"
                name="picture"
                onChange={event => {
                  const file = event.target.files[0];
                  const base64 = convertBase64(file);
                  base64.then(data => {
                    setUploadedPicture(data);
                    setShowCropper(true);
                  });
                  formikRef.current.setFieldValue(
                    'picture',
                    event.target.files[0],
                  );
                }}
              />
            </div>

            {showCropper && (
              <Modal
                show={showCropper}
                onHide={() => setShowCropper(false)}
                centered
                backdrop="static"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Recadrer l'image</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: '400px',
                      margin: '0 auto',
                    }}
                  > 
                      <Cropper
                        src={uploadedPicture}
                        ref={cropperRef}
                        style={{
                          width: '100%',
                          height: '100%',
                        }}
                        aspectRatio={1}
                        guides={false}
                        cropBoxMovable={true}
                        cropBoxResizable={true}
                        viewMode={1}
                        background={false}
                        autoCropArea={1}
                        ready={() => {
                          const cropBox = document.querySelector(
                            '.cropper-crop-box',
                          );
                          const viewBox = document.querySelector(
                            '.cropper-view-box',
                          );
                          if (cropBox) {
                            cropBox.style.borderRadius = '50%';
                          }
                          if (viewBox) {
                            viewBox.style.borderRadius = '50%';
                            viewBox.style.overflow = 'hidden';
                          }
                        }}
                      />
                    </div>
                  </Modal.Body>
                  <Modal.Footer>
                    <Button
                      variant="secondary"
                      onClick={() => setShowCropper(false)}
                    >
                      Annuler
                    </Button>
                    <Button variant="primary" onClick={handleSaveCroppedImage}>
                      Sauvegarder
                    </Button>
                  </Modal.Footer>
                </Modal>
              )}

              {(isUpdateMode || isReadMode) && (
                <div className="form-group">
                  <label htmlFor="firstName" style={labelStyle}>
                    Nom <span className="text-danger">*</span>
                  </label>
                  <Field
                    id="firstName"
                    name="firstName"
                    type="text"
                    className="form-control input-border"
                    placeholder="Nom de l'assistant"
                    style={inputStyle}
                    disabled={isReadMode || isUpdateMode}
                  />
                  {errors.firstName && touched.firstName && (
                    <div style={dangerStyle} className="text-danger">
                      {errors.firstName}
                    </div>
                  )}
                </div>
              )}
              {(isUpdateMode || isReadMode) && (
                <div className="form-group">
                  <label htmlFor="lastName" style={labelStyle}>
                    Prénom <span className="text-danger">*</span>
                  </label>
                  <Field
                    id="lastName"
                    name="lastName"
                    type="text"
                    className="form-control"
                    placeholder="Prénom de l'assistant"
                    style={inputStyle}
                    disabled={isReadMode || isUpdateMode}
                  />
                  {errors.lastName && touched.lastName && (
                    <div style={dangerStyle} className="text-danger">
                      {errors.lastName}
                    </div>
                  )}
                </div>
              )}

              <div className="form-group">
                <label htmlFor="email" style={labelStyle}>
                  Email <span className="text-danger">*</span>
                </label>
                <Field
                  id="email"
                  name="email"
                  type="email"
                  className="form-control"
                  placeholder="Email de l'assistant"
                  style={inputStyle}
                  disabled={isReadMode || isUpdateMode}
                />
                {errors.email && touched.email && (
                  <div style={dangerStyle} className="text-danger">
                    {errors.email}
                  </div>
                )}
              </div>
              <div className="form-group">
                <label htmlFor="phone" style={labelStyle}>
               Numéro de téléphone
                </label>
                <Field
                  id="phone"
                  name="phone"
                  type="text"
                  className="form-control"
                  placeholder="Numéro de téléphone de l'assistant"
                  style={inputStyle}
                  disabled={isReadMode}
                />
           
              </div>
              <div className="form-group">
                <label htmlFor="cinNumber" style={labelStyle}>
                Numéro de CIN
                </label>
                <Field
                  id="cinNumber"
                  name="cinNumber"
                  type="text"
                  className="form-control"
                  placeholder="CIN de l'assistant"
                  style={inputStyle}
                  disabled={isReadMode}
                />
              
              </div>
              <div className="form-group">
                <label htmlFor="birthDate" style={labelStyle}>
                  Date de naissance{' '}
                </label>
                <Field
                  id="birthDate"
                  name="birthDate"
                  type="date"
                  className="form-control"
                  style={isReadMode ? disabledInputStyle : inputStyle}
                  disabled={isReadMode}
                />
              </div>

            <div className="form-group">
              <label htmlFor="address" style={labelStyle}>
                Adresse
              </label>
              <Field
                id="address"
                name="address"
                disabled={isReadMode}
                type="text"
                className="form-control"
                placeholder="Adresse de l'assistant"
                style={inputStyle}
              />
            </div>

            <div className="form-group">
              <SchoolLevels
                label="Education"
                name="schoolLevel.id"
                educationType="member"
                disabled={isReadMode}
              />
            </div>
            <div className="form-group">
              <label htmlFor="languageCommunicationIds" style={labelStyle}>
                Langues
              </label>
              <Select
                id="languageCommunicationIds"
                placeholder="Sélectionnez les langues associées"
                isMulti
                name="languageCommunicationIds"
                className="basic-multi-select"
                classNamePrefix="select"
                options={languageOptions}
                value={languageOptions.filter(el =>
                  values.languageCommunicationIds.includes(el.value),
                )}
                onChange={options =>
                  setFieldValue(
                    'languageCommunicationIds',
                    options && options.map(option => option.value),
                  )
                }
                styles={{
                  control: provided => ({
                    ...provided,
                    borderRadius: '35px',
                    borderColor: '#4F89D780',
                  }),
                  multiValue: provided => ({
                    ...provided,
                    borderRadius: '20px',
                    padding: '0 5px',
                  }),
                  multiValueRemove: (provided) => ({
                    ...provided,
                    borderRadius: '50%',
                  }),
                }}
                isDisabled={isReadMode}
                noOptionsMessage={() => 'Aucune option disponible'}
              />
            </div>

            <div className="d-flex justify-content-end align-items-center gap-10">
              {!isReadMode && (
                <button
                  type="button"
                  className="btn-style secondary"
                  onClick={handleCancel}
                >
                  Annuler
                </button>
              )}
              {isReadMode ? (
                <div className="d-flex flex-row justify-content-center align-items-center">
                  <button
                    type="button"
                    className="btn-style secondary mx-2"
                    onClick={handleCancel}
                  >
                    Quitter
                  </button>
                  <button
                    type="button"
                    className="btn-style primary"
                    onClick={() =>
                      handelChangerZone(
                        values.id,
                        values.zoneId,
                        values.dateAffectationToZone,
                        values.dateEndAffectationToZone,
                      )
                    }
                  >
                    {values.zoneId ? 'Modifier la zone' : 'Affecter une zone'}
                  </button>
                  {showChangeZoneModal && (
                    <ZoneChangeModal
                      show={showChangeZoneModal}
                      onClose={handleCloseChangeZoneModal}
                      assistantId={assistantIdForChangeZone}
                      zoneId={zoneIdForChangeZone}
                      zoneName={
                        isUpdateMode && values.zoneId
                          ? (
                              zones.find(zone => zone.id === values.zoneId) ||
                              {}
                            ).name || ''
                          : ''
                      }
                      dateAffectationToZone={dateAffectationForChangeZone}
                      endDate={endDateForChangeZone}
                      onChangeZone={(newZoneId, endDate, newZoneDate) => {
                        dispatch(
                          changeZoneRequest({
                            assistantId: assistantIdForChangeZone,
                            endDate,
                            newZoneId,
                            newZoneDate,
                          }),
                        );
                      }}
                    />
                  )}
                </div>
              ) : (
                <button
                  type="submit"
                  className="btn-style primary"
                  disabled={formSubmitted}
                >
                  {isUpdateMode ? 'Modifier' : 'Ajouter'}
                </button>
              )}
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}
