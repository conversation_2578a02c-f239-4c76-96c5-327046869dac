import React, { useEffect, useState } from 'react';
import styles from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import saga from 'containers/Common/SubComponents/SousZoneForm/saga';
import reducer from 'containers/Common/SubComponents/SousZoneForm/reducer';
import sagaFich from "containers/EspaceAssistant/FicheAssistant/saga"
import reducerFich from "containers/EspaceAssistant/FicheAssistant/reducer"
import { useParams } from 'react-router-dom';
import {
  deleteSousZone,
  resetAddSousZone,
  resetDeleteSousZone,
} from 'containers/Common/SubComponents/SousZoneForm/actions';
import { Alert } from 'react-bootstrap';
import CustomPagination from '../../../Common/CustomPagination';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Collapse,
} from '@mui/material';
import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';

import {
  makeSelectSousZone,
  makeSelectSousZoneDeleteSuccess,
  makeSelectError,
  makeSelectSuccess, 
} from 'containers/Common/SubComponents/SousZoneForm/selectors';
import {
  makeSelectZonesWithSousZones,
} from 'containers/EspaceAssistant/FicheAssistant/selectors'
import { loadSousZones,loadZonesWithSousZones } from 'containers/EspaceAssistant/FicheAssistant/actions';
import SousZoneForm from 'containers/Common/SubComponents/SousZoneForm';
import {
  DELETE_ICON,
  EDIT_ICON,
} from '../../../Common/ListIcons/ListIcons';

const key = 'sousZone';

const emptyValue = <span>-</span>;

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  sousZone: makeSelectSousZone,
  successDelete: makeSelectSousZoneDeleteSuccess, 
  zonesWithSousZones: makeSelectZonesWithSousZones()
});




export default function SousZones(props) {
  const dispatch = useDispatch();
  const zoneId = props.zoneId;
  const assistantId=props.assistantId;
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useInjectReducer({ key: 'assistantFiche', reducer: reducerFich });
  useInjectSaga({ key: 'assistantFiche', saga: sagaFich });

  const params = useParams();
  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const { success, error, sousZone, successDelete,zonesWithSousZones } = useSelector(omdbSelector);
  const [sousZoneToEdit, setSousZoneToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [sousZoneToDelete, setSousZoneToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [expandedZones, setExpandedZones] = useState({});

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;
  const sousZones = props.sousZones;
  const handleClose = () => setShow(false);

  useEffect(() => {
    if (successDelete) { 
      setShowAlert(true);
      dispatch(loadSousZones(zoneId));
      // dispatch(loadZonesWithSousZones(assistantId));
      setMessage('Sous Zone supprimée avec succès');
      dispatch(resetDeleteSousZone());
    }
  }, [successDelete, dispatch, sousZone, params.id]);

  // add teh error check here
  useEffect(() => {
    if (error)
    {
      // the error should inclue this "his SousZone is atta"
      if (error.includes('It cannot be deleted')) {
        setShowErrorAlert(true);
        setErrorMessage(
          'Cette Sous Zone est attachée à un ou plusieurs bénéficiaires et ne peut pas être supprimée',
        );
      }
      dispatch(resetDeleteSousZone());
    }
  }, [error, dispatch, sousZone, params.id]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      setMessage(
        sousZoneToEdit
          ? 'Sous Zone modifiée avec succès'
          : 'Sous Zone ajoutée avec succès',
      );
      setSousZoneToEdit('');
      dispatch(loadSousZones(zoneId));
      dispatch(resetAddSousZone());
    }
  }, [success, dispatch, sousZoneToEdit, sousZone, params.id]);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => setShowAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if (showErrorAlert) {
      const timer = setTimeout(() => setShowErrorAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showErrorAlert]);

  useEffect(() => {
    dispatch(loadSousZones(zoneId));
    dispatch(loadZonesWithSousZones(assistantId));
  }, [dispatch, zoneId,assistantId]);

  console.log({ sousZones });

  const toggleZoneExpansion = zoneId => {
    setExpandedZones(prev => ({
      ...prev,
      [zoneId]: !prev[zoneId],
    }));
  };

  const editSousZoneHandler = sousZone => {
    setSousZoneToEdit(sousZone);
    setShow(true);
  };

  const deleteSousZoneHandler = sousZone => {
    setSousZoneToDelete(sousZone);
    setShowDeleteModal(true);
  };

  let listZones = [];
  
  if (zonesWithSousZones && Array.isArray(zonesWithSousZones)) {
    // Create a shallow copy of the array before sorting it
    const sortedZones = [...zonesWithSousZones].sort((a, b) => b.id - a.id);

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, sortedZones.length);
    const paginatedZones = sortedZones.slice(startIndex, endIndex);

    listZones = paginatedZones.map(zone => ({
      id: zone.id,
      name: zone.name || emptyValue,
      nameAr: zone.nameAr || emptyValue,
      code: zone.code || emptyValue,
      dateAffectation: zone.dateAffectation ? formatDate(zone.dateAffectation) : emptyValue,
      dateFinAffectation: zone.dateFinAffectation ? formatDate(zone.dateFinAffectation) : emptyValue,
      zoneDTOList: zone.zoneDTOList || [],
    }));
  }



  return (
    <div>
      {showAlert && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}
      {showErrorAlert && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowErrorAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}
      <div className={styles.backgroudStyle}>
        <div className={styles.global}>
          <div className={styles.header}>
            <h4>Liste des Zones</h4>
            <button
              className={btnStyles.addBtnProfile}
              onClick={() => {
                setShow(true);
                setSousZoneToEdit('');
              }}
            >
              Ajouter
            </button>
          </div>
          <TableContainer component={Paper} style={{ backgroundColor: 'white' }}>
            <Table>
              <TableHead>
                <TableRow style={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell style={{ width: '60px' }}></TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Nom</TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Nom Arabe</TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Code</TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Date d'affectation</TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Date fin d'affectation</TableCell>
                  <TableCell align="center" style={{ fontWeight: 'bold' }}>Sous-zones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {listZones.map(zone => (
                  <React.Fragment key={zone.id}>
                    <TableRow>
                      <TableCell>
                        {zone.zoneDTOList && zone.zoneDTOList.length > 0 ? (
                          <IconButton
                            size="small"
                            onClick={() => toggleZoneExpansion(zone.id)}
                          >
                            {expandedZones[zone.id] ? (
                              <KeyboardArrowUp />
                            ) : (
                              <KeyboardArrowDown />
                            )}
                          </IconButton>
                        ) : null}
                      </TableCell>
                      <TableCell align="center">{zone.name}</TableCell>
                      <TableCell align="center">{zone.nameAr}</TableCell>
                      <TableCell align="center">{zone.code}</TableCell>
                      <TableCell align="center">{zone.dateAffectation}</TableCell>
                      <TableCell align="center">{zone.dateFinAffectation}</TableCell>
                      <TableCell align="center">
                        <span style={{
                          backgroundColor: zone.zoneDTOList && zone.zoneDTOList.length > 0 ? '#e3f2fd' : '#f5f5f5',
                          color: zone.zoneDTOList && zone.zoneDTOList.length > 0 ? '#1976d2' : '#666',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '0.85rem',
                          fontWeight: 'bold'
                        }}>
                          {zone.zoneDTOList ? zone.zoneDTOList.length : 0}
                        </span>
                      </TableCell>
                    </TableRow>

                    {/* Sous-zones Rows */}
                    {zone.zoneDTOList && zone.zoneDTOList.length > 0 && (
                      <TableRow>
                        <TableCell colSpan={7}>
                          <Collapse
                            in={expandedZones[zone.id]}
                            timeout="auto"
                            unmountOnExit
                          >
                            <Table size="small" style={{ marginTop: '10px', marginBottom: '10px' }}>
                              <TableHead>
                                <TableRow style={{ backgroundColor: '#e8f4fd' }}>
                                  <TableCell align="center" style={{ fontWeight: 'bold', fontSize: '0.85rem', color: '#1976d2', padding: '8px' }}>
                                    Code Sous-Zone
                                  </TableCell>
                                  <TableCell align="center" style={{ fontWeight: 'bold', fontSize: '0.85rem', color: '#1976d2', padding: '8px' }}>
                                    Nom
                                  </TableCell>
                                  <TableCell align="center" style={{ fontWeight: 'bold', fontSize: '0.85rem', color: '#1976d2', padding: '8px' }}>
                                    Nom Arabe
                                  </TableCell>
                                  <TableCell align="center" style={{ fontWeight: 'bold', fontSize: '0.85rem', color: '#1976d2', padding: '8px' }}>
                                    Détail
                                  </TableCell>
                                  <TableCell align="center" style={{ fontWeight: 'bold', fontSize: '0.85rem', color: '#1976d2', padding: '8px' }}>
                                    Actions
                                  </TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {zone.zoneDTOList && zone.zoneDTOList.length > 0 ? (
                                  zone.zoneDTOList.map(sousZone => (
                                  <TableRow
                                    key={sousZone.id}
                                    style={{
                                      backgroundColor: '#f8f9fa',
                                      '&:hover': { backgroundColor: '#e9ecef' }
                                    }}
                                  >
                                    <TableCell align="center" style={{ padding: '8px', fontSize: '0.85rem' }}>
                                      {sousZone.code || emptyValue}
                                    </TableCell>
                                    <TableCell align="center" style={{ padding: '8px', fontSize: '0.85rem' }}>
                                      {sousZone.name || emptyValue}
                                    </TableCell>
                                    <TableCell align="center" style={{ padding: '8px', fontSize: '0.85rem' }}>
                                      {sousZone.nameAr || emptyValue}
                                    </TableCell>
                                    <TableCell align="center" style={{ padding: '8px', fontSize: '0.85rem' }}>
                                      {sousZone.detail || emptyValue}
                                    </TableCell>
                                    <TableCell align="center" style={{ padding: '8px' }}>
                                      <input
                                        type="image"
                                        src={EDIT_ICON}
                                        width="25px"
                                        height="25px"
                                        className="p-1"
                                        title="Modifier Sous-Zone"
                                        onClick={() => editSousZoneHandler(sousZone)}
                                        style={{ marginRight: '5px', cursor: 'pointer' }}
                                      />
                                      <input
                                        type="image"
                                        src={DELETE_ICON}
                                        width="25px"
                                        height="25px"
                                        className="p-1"
                                        title="Supprimer Sous-Zone"
                                        onClick={() => deleteSousZoneHandler(sousZone)}
                                        style={{ cursor: 'pointer' }}
                                      />
                                    </TableCell>
                                  </TableRow>
                                  ))
                                ) : (
                                  <TableRow>
                                    <TableCell colSpan={5} align="center" style={{ padding: '16px', fontStyle: 'italic', color: '#666' }}>
                                      Aucune sous-zone disponible
                                    </TableCell>
                                  </TableRow>
                                )}
                              </TableBody>
                            </Table>
                          </Collapse>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <CustomPagination
              totalCount={Math.ceil(
                (zonesWithSousZones && Array.isArray(zonesWithSousZones) ? zonesWithSousZones.length : 0) / pageSize,
              )}
              pageSize={pageSize}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              // should we pass the handleClose function here?
              handleClose={handleClose}
            />
          </div>
        </div>
      </div>

      <Modal2
        title={
          sousZoneToEdit ? 'Modifier la Sous Zone' : 'Ajouter une Sous Zone'
        }
        show={show}
        handleClose={handleClose}
      >
        <SousZoneForm
          zoneId={sousZoneToEdit ? zoneId : null}
          handleClose={handleClose}
          sousZoneToEdit={sousZoneToEdit}
        />
      </Modal2>

      <Modal2
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={() => setShowDeleteModal(false)}
      >
        <p>Êtes-vous sûr de vouloir supprimer cette Sous Zone ?</p>
        <div>
          <button
            className={btnStyles.cancelBtn}
            onClick={() => setShowDeleteModal(false)}
          >
            Annuler
          </button>
          <button
            className={btnStyles.deleteBtn}
            onClick={() => {
              dispatch(deleteSousZone(sousZoneToDelete.id));
              setShowDeleteModal(false);
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
    </div>
  );
}
